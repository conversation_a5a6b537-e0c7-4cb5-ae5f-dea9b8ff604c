<?php

namespace App\Imports;

use App\Models\Outlet;
use App\Models\OutletProduct;
use App\Models\Product;
use App\Models\ReportStockDetail;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Row;
use Maatwebsite\Excel\Concerns\OnEachRow;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithBatchInserts;

class ReportStockBulkImport implements OnEachRow, WithHeadingRow, WithChunkReading, WithBatchInserts
{
    protected $reportStockId;

    protected $outletCache;
    protected $productCache;
    protected $outletProductCache;

    protected $productsToInsert = [];
    protected $productsToUpdate = [];
    protected $outletProductsToUpsert = [];
    protected $reportStockDetailsToUpsert = [];

    protected $rowCount = 0;

    public function __construct($reportStockId)
    {
        $this->reportStockId = $reportStockId;
        $this->outletCache = collect();
        $this->productCache = collect();
        $this->outletProductCache = collect();
    }

    public function onRow(Row $row)
    {
        $this->rowCount++;

        $data = $row->toArray();
        $outletCode = $data['outlet_code'] ?? null;
        $barcode    = $data['barcode'] ?? null;

        if (!$outletCode || !$barcode) {
            return;
        }

        $outlet = $this->getOrValidateOutlet($outletCode);
        if (!$outlet) {
            return;
        }

        $productId = $this->getOrPrepareProduct($data);

        $outletProductKey = $outlet->id . '-' . $productId;
        if (!$this->outletProductCache->has($outletProductKey)) {
            $this->outletProductsToUpsert[] = [
                'outlet_id'  => $outlet->id,
                'product_id' => $productId,
            ];
            $this->outletProductCache->put($outletProductKey, true);
        }

        $this->reportStockDetailsToUpsert[] = [
            'report_stock_id' => $this->reportStockId,
            'outlet_id'       => $outlet->id,
            'product_id'      => $productId,
            'stock'           => $data['stock'] ?? 0,
        ];

        // flush batch setiap 2000 row
        if (
            count($this->productsToInsert) >= 2000 ||
            count($this->productsToUpdate) >= 2000 ||
            count($this->outletProductsToUpsert) >= 2000 ||
            count($this->reportStockDetailsToUpsert) >= 2000
        ) {
            $this->flushBatch();
        }
    }

    protected function getOrValidateOutlet($outletCode)
    {
        if ($this->outletCache->has($outletCode)) {
            return $this->outletCache->get($outletCode);
        }

        $outlet = Outlet::where('code', $outletCode)->first();
        if ($outlet) {
            $this->outletCache->put($outletCode, $outlet);
        }
        return $outlet;
    }

    protected function getOrPrepareProduct($row)
    {
        $barcode = $row['barcode'];
        $product = $this->productCache->get($barcode);

        if (!$product) {
            // produk baru
            $this->productsToInsert[$barcode] = [
                'barcode' => $barcode,
                'name'    => $row['product_name'] ?? null,
                'unit'    => $row['unit'] ?? null,
                'hpp'     => $row['hpp'] ?? 0,
            ];
            // simpan pakai placeholder sementara
            return 'temp_' . $barcode;
        }

        // cek perubahan
        $updates = [];
        if ($product->name !== ($row['product_name'] ?? null)) {
            $updates['name'] = $row['product_name'];
        }
        if ($product->unit !== ($row['unit'] ?? null)) {
            $updates['unit'] = $row['unit'];
        }
        if ($product->hpp != ($row['hpp'] ?? 0)) {
            $updates['hpp'] = $row['hpp'];
        }

        if (!empty($updates)) {
            $updates['id']      = $product->id;
            $updates['barcode'] = $product->barcode;
            $this->productsToUpdate[$product->id] = $updates;
        }

        return $product->id;
    }

    protected function flushBatch()
    {
        DB::transaction(function () {
            // insert produk baru
            if (!empty($this->productsToInsert)) {
                Product::insertOrIgnore(array_values($this->productsToInsert));

                $barcodes = array_keys($this->productsToInsert);
                $newProducts = Product::whereIn('barcode', $barcodes)->get()->keyBy('barcode');
                $this->productCache = $this->productCache->merge($newProducts);

                foreach ($this->reportStockDetailsToUpsert as &$detail) {
                    if (is_string($detail['product_id']) && str_starts_with($detail['product_id'], 'temp_')) {
                        $barcode = substr($detail['product_id'], 5);
                        $detail['product_id'] = $newProducts[$barcode]->id ?? null;
                    }
                }
                unset($detail);

                foreach ($this->outletProductsToUpsert as &$op) {
                    if (is_string($op['product_id']) && str_starts_with($op['product_id'], 'temp_')) {
                        $barcode = substr($op['product_id'], 5);
                        $op['product_id'] = $newProducts[$barcode]->id ?? null;
                    }
                }
                unset($op);

                $this->productsToInsert = [];
            }

            // update produk existing
            if (!empty($this->productsToUpdate)) {
                Product::upsert(array_values($this->productsToUpdate), ['id'], ['name', 'unit', 'hpp']);
                $this->productsToUpdate = [];
            }

            // upsert outlet products
            if (!empty($this->outletProductsToUpsert)) {
                OutletProduct::upsert($this->outletProductsToUpsert, ['outlet_id', 'product_id']);
                $this->outletProductsToUpsert = [];
            }

            // upsert report stock details
            if (!empty($this->reportStockDetailsToUpsert)) {
                ReportStockDetail::upsert(
                    $this->reportStockDetailsToUpsert,
                    ['report_stock_id', 'outlet_id', 'product_id'],
                    ['stock']
                );
                $this->reportStockDetailsToUpsert = [];
            }
        });
    }

    public function chunkSize(): int
    {
        return 2000; // baca 2000 row sekali
    }

    public function batchSize(): int
    {
        return 2000; // insert 2000 sekali
    }

    public function __destruct()
    {
        // flush sisa batch terakhir
        $this->flushBatch();

        Log::info("Import selesai: {$this->rowCount} row diproses.");
    }
}
