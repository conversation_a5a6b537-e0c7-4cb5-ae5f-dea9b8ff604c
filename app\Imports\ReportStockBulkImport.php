<?php

namespace App\Imports;

use App\Models\Outlet;
use App\Models\Product;
use App\Models\OutletProduct;
use App\Models\ReportStock;
use App\Models\ReportStockDetail;
use App\Services\ImportOptimizationService;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;
use Maatwebsite\Excel\Concerns\WithBatchInserts;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\SkipsEmptyRows;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;

class ReportStockBulkImport implements ToCollection, WithHeadingRow, WithValidation, WithBatchInserts, WithChunkReading, SkipsEmptyRows
{
    protected $reportDate;
    protected $processedRows = 0;
    protected $productsCreated = 0;
    protected $outletProductsCreated = 0;
    protected $outletProductsUpdated = 0;
    protected $reportStocksCreated = 0;
    protected $reportStocksUpdated = 0;
    protected $errors = [];

    // Cache untuk menghindari query berulang
    protected $outletCache = [];
    protected $productCache = [];
    protected $outletProductCache = [];
    protected $reportStockCache = null;

    // Batch data untuk bulk operations
    protected $productsToCreate = [];
    protected $outletProductsToUpsert = [];
    protected $reportStockDetailsToUpsert = [];

    // Optimization service
    protected $optimizationService;

    public function __construct(string $reportDate)
    {
        $this->reportDate = $reportDate;
        $this->optimizationService = new ImportOptimizationService();
    }

    public function collection(Collection $rows)
    {
        // Optimize environment for large import
        $this->optimizationService->optimizeForImport('report_stock_bulk');

        try {
            // Pre-load all necessary data
            $this->preloadCaches($rows);

            // Process rows in chunks
            $chunks = $rows->chunk(1000);

            foreach ($chunks as $chunk) {
                $this->processChunk($chunk);
            }

            // Final batch operations
            $this->executeFinalBatchOperations();

            Log::info('ReportStockBulkImport completed successfully', [
                'processed_rows' => $this->processedRows,
                'products_created' => $this->productsCreated,
                'outlet_products_created' => $this->outletProductsCreated,
                'outlet_products_updated' => $this->outletProductsUpdated,
                'report_stocks_created' => $this->reportStocksCreated,
                'report_stocks_updated' => $this->reportStocksUpdated,
            ]);

        } catch (\Exception $e) {
            Log::error('ReportStockBulkImport failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            throw $e;
        } finally {
            // Restore original settings
            $this->optimizationService->restoreSettings();
        }
    }

    protected function preloadCaches(Collection $rows)
    {
        // Get unique outlet codes and barcodes from the data
        $outletCodes = $rows->pluck('outlet')->filter()->unique()->values();
        $barcodes = $rows->pluck('barcode')->filter()->unique()->values();

        // Preload outlets
        $outlets = Outlet::whereIn('code', $outletCodes)->get();
        foreach ($outlets as $outlet) {
            $this->outletCache[$outlet->code] = $outlet;
        }

        // Preload products
        $products = Product::whereIn('barcode', $barcodes)->get();
        foreach ($products as $product) {
            $this->productCache[$product->barcode] = $product;
        }

        // Preload outlet products for existing combinations
        $outletIds = $outlets->pluck('id')->toArray();
        $productIds = $products->pluck('id')->toArray();

        if (!empty($outletIds) && !empty($productIds)) {
            $outletProducts = OutletProduct::whereIn('outlet_id', $outletIds)
                ->whereIn('product_id', $productIds)
                ->get();

            foreach ($outletProducts as $op) {
                $this->outletProductCache["{$op->outlet_id}_{$op->product_id}"] = $op;
            }
        }

        // Get or create report stock for the date
        $this->reportStockCache = ReportStock::firstOrCreate(
            ['report_date' => $this->reportDate],
            ['is_generated' => false]
        );

        if ($this->reportStockCache->wasRecentlyCreated) {
            $this->reportStocksCreated++;
        }

        Log::info('Cache preloaded', [
            'outlets' => count($this->outletCache),
            'products' => count($this->productCache),
            'outlet_products' => count($this->outletProductCache),
        ]);
    }

    protected function processChunk(Collection $chunk)
    {
        foreach ($chunk as $row) {
            try {
                $this->processRow($row);
                $this->processedRows++;
            } catch (\Exception $e) {
                $this->errors[] = [
                    'row' => $this->processedRows + 1,
                    'error' => $e->getMessage(),
                    'data' => $row->toArray(),
                ];
                Log::warning('Error processing row', [
                    'row' => $this->processedRows + 1,
                    'error' => $e->getMessage(),
                    'data' => $row->toArray(),
                ]);
            }
        }

        // Execute batch operations every 1000 rows
        if (count($this->productsToCreate) >= 1000) {
            $this->batchCreateProducts();
        }
        if (count($this->outletProductsToUpsert) >= 1000) {
            $this->batchUpsertOutletProducts();
        }
        if (count($this->reportStockDetailsToUpsert) >= 1000) {
            $this->batchUpsertReportStockDetails();
        }
    }

    protected function processRow(Collection $row)
    {
        // Validate required fields
        $outletCode = trim($row['outlet'] ?? '');
        $barcode = trim($row['barcode'] ?? '');
        $productName = trim($row['nama_produk'] ?? '');
        $quantity = (int) ($row['qty'] ?? 0);

        if (empty($outletCode) || empty($barcode) || empty($productName)) {
            throw new \Exception('Missing required fields: outlet, barcode, or product name');
        }

        // Get or validate outlet
        $outlet = $this->getOrValidateOutlet($outletCode);
        if (!$outlet) {
            throw new \Exception("Outlet not found: {$outletCode}");
        }

        // Get or create product
        $product = $this->getOrCreateProduct($row);

        // Get or create outlet product relationship
        $this->getOrCreateOutletProduct($outlet, $product, $row);

        // Add to report stock details batch
        $this->addToReportStockDetailsBatch($outlet, $product, $quantity);
    }

    protected function getOrValidateOutlet(string $outletCode): ?Outlet
    {
        if (isset($this->outletCache[$outletCode])) {
            return $this->outletCache[$outletCode];
        }

        // Try to find outlet in database if not in cache
        $outlet = Outlet::where('code', $outletCode)->first();
        if ($outlet) {
            $this->outletCache[$outletCode] = $outlet;
        }

        return $outlet;
    }

    protected function getOrCreateProduct(Collection $row): Product
    {
        $barcode = trim($row['barcode']);

        if (isset($this->productCache[$barcode])) {
            $product = $this->productCache[$barcode];

            // Check if product needs update
            $needsUpdate = false;
            $updates = [];

            if (!empty($row['nama_produk']) && $product->name !== trim($row['nama_produk'])) {
                $updates['name'] = trim($row['nama_produk']);
                $needsUpdate = true;
            }

            if (!empty($row['sat']) && $product->unit !== trim($row['sat'])) {
                $updates['unit'] = trim($row['sat']);
                $needsUpdate = true;
            }

            // Parse pack quantity (format: "1,00" -> 1)
            $packQuantity = $this->parsePackQuantity($row['pack'] ?? '1');
            if ($product->pack_quantity !== $packQuantity) {
                $updates['pack_quantity'] = $packQuantity;
                $needsUpdate = true;
            }

            if ($needsUpdate) {
                $product->update($updates);
                // Update cache
                $this->productCache[$barcode] = $product->fresh();
            }

            return $this->productCache[$barcode] ?? $product;
        }

        // Product doesn't exist, add to batch creation
        $productData = [
            'name' => trim($row['nama_produk']),
            'barcode' => $barcode,
            'unit' => trim($row['sat'] ?? ''),
            'pack_quantity' => $this->parsePackQuantity($row['pack'] ?? '1'),
            'created_at' => now(),
            'updated_at' => now(),
        ];

        $this->productsToCreate[] = $productData;

        // Create temporary product object for immediate use
        $product = new Product($productData);
        $product->id = 'temp_' . $barcode; // Temporary ID
        $this->productCache[$barcode] = $product;

        return $product;
    }

    protected function getOrCreateOutletProduct(Outlet $outlet, Product $product, Collection $row): void
    {
        $cacheKey = "{$outlet->id}_{$product->id}";

        if (isset($this->outletProductCache[$cacheKey])) {
            $outletProduct = $this->outletProductCache[$cacheKey];

            // Check if outlet product needs update
            $needsUpdate = false;
            $updates = [];

            if (!empty($row['prt']) && $outletProduct->outlet_pareto !== trim($row['prt'])) {
                $updates['outlet_pareto'] = trim($row['prt']);
                $needsUpdate = true;
            }

            if ($needsUpdate) {
                $updates['updated_at'] = now();
                $this->outletProductsToUpsert[] = array_merge([
                    'outlet_id' => $outlet->id,
                    'product_id' => $product->id,
                ], $updates);
                $this->outletProductsUpdated++;
            }

            return;
        }

        // OutletProduct doesn't exist, add to batch upsert
        $outletProductData = [
            'outlet_id' => $outlet->id,
            'product_id' => $product->id,
            'outlet_pareto' => trim($row['prt'] ?? ''),
            'rumus_pareto' => null,
            'min_buffer' => 0,
            'max_buffer' => 0,
            'created_at' => now(),
            'updated_at' => now(),
        ];

        $this->outletProductsToUpsert[] = $outletProductData;
        $this->outletProductsCreated++;

        // Add to cache
        $outletProduct = new OutletProduct($outletProductData);
        $this->outletProductCache[$cacheKey] = $outletProduct;
    }

    protected function addToReportStockDetailsBatch(Outlet $outlet, Product $product, int $quantity): void
    {
        $this->reportStockDetailsToUpsert[] = [
            'report_stock_id' => $this->reportStockCache->id,
            'outlet_id' => $outlet->id,
            'product_id' => $product->id,
            'quantity' => $quantity,
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }

    protected function batchCreateProducts(): void
    {
        if (empty($this->productsToCreate)) {
            return;
        }

        // Insert products in batches
        $chunks = array_chunk($this->productsToCreate, 1000);

        foreach ($chunks as $chunk) {
            Product::insert($chunk);
            $this->productsCreated += count($chunk);
        }

        // Update cache with real IDs
        $barcodes = array_column($this->productsToCreate, 'barcode');
        $newProducts = Product::whereIn('barcode', $barcodes)->get();

        $oldToNewProductIds = [];
        foreach ($newProducts as $product) {
            $oldTempId = 'temp_' . $product->barcode;
            $oldToNewProductIds[$oldTempId] = $product->id;
            $this->productCache[$product->barcode] = $product;
        }

        // Update outlet products with real product IDs
        foreach ($this->outletProductsToUpsert as &$outletProduct) {
            if (isset($oldToNewProductIds[$outletProduct['product_id']])) {
                $outletProduct['product_id'] = $oldToNewProductIds[$outletProduct['product_id']];
            }
        }

        // Update report stock details with real product IDs
        foreach ($this->reportStockDetailsToUpsert as &$reportStockDetail) {
            if (isset($oldToNewProductIds[$reportStockDetail['product_id']])) {
                $reportStockDetail['product_id'] = $oldToNewProductIds[$reportStockDetail['product_id']];
            }
        }

        // Clear batch
        $this->productsToCreate = [];

        Log::info('Batch created products', ['count' => $this->productsCreated]);
    }

    protected function batchUpsertOutletProducts(): void
    {
        if (empty($this->outletProductsToUpsert)) {
            return;
        }

        // Filter out invalid data and ensure all records have the same structure
        $validData = [];
        foreach ($this->outletProductsToUpsert as $data) {
            // Skip if product_id is temporary (string)
            if (is_string($data['product_id']) && str_starts_with($data['product_id'], 'temp_')) {
                continue;
            }

            // Ensure all required fields are present
            $validData[] = [
                'outlet_id' => $data['outlet_id'],
                'product_id' => $data['product_id'],
                'outlet_pareto' => $data['outlet_pareto'] ?? '',
                'rumus_pareto' => $data['rumus_pareto'] ?? null,
                'min_buffer' => $data['min_buffer'] ?? 0,
                'max_buffer' => $data['max_buffer'] ?? 0,
                'created_at' => $data['created_at'] ?? now(),
                'updated_at' => $data['updated_at'] ?? now(),
            ];
        }

        if (!empty($validData)) {
            // Use upsert for outlet products
            OutletProduct::upsert(
                $validData,
                ['outlet_id', 'product_id'], // Unique keys
                ['outlet_pareto', 'rumus_pareto', 'min_buffer', 'max_buffer', 'updated_at'] // Update fields
            );
        }

        // Clear batch
        $this->outletProductsToUpsert = [];

        Log::info('Batch upserted outlet products', [
            'valid_records' => count($validData),
            'created' => $this->outletProductsCreated,
            'updated' => $this->outletProductsUpdated,
        ]);
    }

    protected function batchUpsertReportStockDetails(): void
    {
        if (empty($this->reportStockDetailsToUpsert)) {
            return;
        }

        // Filter out invalid data and ensure all records have the same structure
        $validData = [];
        foreach ($this->reportStockDetailsToUpsert as $data) {
            // Skip if product_id is temporary (string)
            if (is_string($data['product_id']) && str_starts_with($data['product_id'], 'temp_')) {
                continue;
            }

            // Ensure all required fields are present
            $validData[] = [
                'report_stock_id' => $data['report_stock_id'],
                'outlet_id' => $data['outlet_id'],
                'product_id' => $data['product_id'],
                'quantity' => $data['quantity'] ?? 0,
                'created_at' => $data['created_at'] ?? now(),
                'updated_at' => $data['updated_at'] ?? now(),
            ];
        }

        if (!empty($validData)) {
            // Use upsert for report stock details
            ReportStockDetail::upsert(
                $validData,
                ['report_stock_id', 'outlet_id', 'product_id'], // Unique keys
                ['quantity', 'updated_at'] // Update fields
            );
        }

        $count = count($validData);
        $this->reportStocksUpdated += $count;

        // Clear batch
        $this->reportStockDetailsToUpsert = [];

        Log::info('Batch upserted report stock details', ['count' => $count]);
    }

    protected function executeFinalBatchOperations(): void
    {
        // Execute remaining batch operations
        $this->batchCreateProducts();
        $this->batchUpsertOutletProducts();
        $this->batchUpsertReportStockDetails();
    }

    // Validation rules
    public function rules(): array
    {
        return [
            'outlet' => 'required|string',
            'barcode' => 'required',
            'nama_produk' => 'required|string',
            'qty' => 'required|numeric',
            'prt' => 'nullable|string',
            'pack' => 'nullable',
            'sat' => 'nullable|string',
        ];
    }

    // Batch configuration
    public function batchSize(): int
    {
        return 1000;
    }

    public function chunkSize(): int
    {
        return 1000;
    }

    // Getter methods for statistics
    public function getProcessedRows(): int
    {
        return $this->processedRows;
    }

    public function getProductsCreated(): int
    {
        return $this->productsCreated;
    }

    public function getOutletProductsCreated(): int
    {
        return $this->outletProductsCreated;
    }

    public function getOutletProductsUpdated(): int
    {
        return $this->outletProductsUpdated;
    }

    public function getReportStocksCreated(): int
    {
        return $this->reportStocksCreated;
    }

    public function getReportStocksUpdated(): int
    {
        return $this->reportStocksUpdated;
    }

    public function getErrors(): array
    {
        return $this->errors;
    }

    /**
     * Parse pack quantity from Excel format (e.g., "1,00" -> 1)
     */
    protected function parsePackQuantity($value): int
    {
        if (is_numeric($value)) {
            return (int) $value;
        }

        if (is_string($value)) {
            // Remove comma and convert to int
            $cleaned = str_replace(',', '.', $value);
            return (int) floatval($cleaned);
        }

        return 1; // Default value
    }
}